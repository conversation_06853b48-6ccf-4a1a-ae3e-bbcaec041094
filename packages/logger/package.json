{"name": "@cindee/logger", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "dev": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "devDependencies": {"@cindee/eslint-config": "workspace:*", "@cindee/prettier-config": "workspace:*", "@cindee/tsconfig": "workspace:*", "@types/node": "^22.18.1", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@cindee/prettier-config", "dependencies": {"pino": "^9.13.1", "pino-opentelemetry-transport": "^1.1.0", "pino-pretty": "^13.1.1"}}