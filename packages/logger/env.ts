import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod/v4";

export function loggerEnv() {
    return createEnv({
        server: {
            OTEL_EXPORTER_OTLP_ENDPOINT: z.string().url().optional(),
            NODE_ENV: z.enum(["development", "production"]).optional(),
            OTEL_EXPORTER_OTLP_LOGS_ENDPOINT: z.string().url().optional(),
            OTEL_RESOURCE_ATTRIBUTES: z.string().optional(),
        },
        experimental__runtimeEnv: {},
        skipValidation:
            !!process.env.CI || process.env.npm_lifecycle_event === "lint",
    });
}
