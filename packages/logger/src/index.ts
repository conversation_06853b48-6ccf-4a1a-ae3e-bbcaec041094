import pino from "pino";
import pinoPretty from "pino-pretty";

import { loggerEnv } from "../env";

const env = loggerEnv();

// const isDev = env.NODE_ENV !== "production";
const isDev = false;

const transportOptions = isDev ?
    {
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'HH:MM:ss Z',
        },
    }
    : {
        target: 'pino-opentelemetry-transport',
        options: {
            level: 'info',
        },
    };

const logger = pino({
    transport: transportOptions,
});

export { logger };
